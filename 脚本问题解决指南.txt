🔧 脚本问题解决指南
====================

如果脚本运行时一闪而过，请按照以下步骤排查：

📋 问题1：脚本一闪而过
======================

原因：脚本遇到错误立即退出，没有显示错误信息

解决方案：
1. 运行诊断脚本：diagnose_setup.bat
2. 查看详细的系统状态和错误信息
3. 根据诊断结果采取相应措施

📋 问题2：Git未安装
===================

症状：脚本提示"Git is not installed"后退出

解决方案：
方法1：自动安装
- 运行 install_git.bat（以管理员身份）

方法2：手动安装
- 访问：https://git-scm.com/download/win
- 下载并安装Git for Windows
- 重启命令提示符

方法3：检查PATH
- Git可能已安装但不在系统PATH中
- 重启命令提示符或重启电脑

📋 问题3：Git仓库未初始化
=========================

症状：脚本提示"not a Git repository"后退出

解决方案：
方法1：自动初始化
- 运行 init_git.bat（以管理员身份）

方法2：手动初始化
- 打开命令提示符
- 运行：git init
- 运行：git add .
- 运行：git commit -m "Initial commit"

📋 问题4：权限不足
==================

症状：脚本无法执行某些操作

解决方案：
- 右键点击脚本文件
- 选择"以管理员身份运行"
- 或者在管理员命令提示符中运行

📋 问题5：网络连接问题
======================

症状：无法访问GitHub或下载失败

解决方案：
- 检查网络连接
- 尝试访问 https://github.com
- 检查防火墙设置
- 尝试使用VPN（如果在某些地区）

📋 推荐的排查步骤
==================

第一步：运行诊断
---------------
双击运行：diagnose_setup.bat
这会告诉您具体缺少什么

第二步：按顺序解决问题
---------------------
根据诊断结果：

如果缺少Git：
→ 运行 install_git.bat

如果缺少Git仓库：
→ 运行 init_git.bat

如果都准备好了：
→ 运行 setup_github_repo.bat

第三步：使用完整设置脚本
-----------------------
运行：complete_setup.bat
这个脚本会自动按正确顺序执行所有步骤

📋 手动验证方法
================

在命令提示符中运行以下命令验证：

检查Git：
git --version

检查仓库：
git status

检查配置：
git config --list

检查远程：
git remote -v

📋 常见错误信息及解决
====================

错误："'git' 不是内部或外部命令"
解决：安装Git或检查PATH环境变量

错误："fatal: not a git repository"
解决：运行 git init 初始化仓库

错误："Please tell me who you are"
解决：配置Git用户信息
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

错误："Permission denied"
解决：以管理员身份运行脚本

错误："Could not resolve hostname"
解决：检查网络连接

📋 获取更多帮助
================

如果问题仍然存在：

1. 查看详细指南：
   - GIT_SETUP_GUIDE.md
   - GITHUB_SETUP_GUIDE.md

2. 运行诊断脚本获取详细信息：
   - diagnose_setup.bat

3. 手动执行每个步骤：
   - 先安装Git
   - 再初始化仓库
   - 最后设置GitHub

4. 检查系统要求：
   - Windows 7/8/10/11
   - 管理员权限
   - 网络连接

💡 小贴士
=========

- 始终以管理员身份运行脚本
- 确保在正确的项目目录中
- 检查网络连接
- 耐心等待下载和安装过程
- 如有疑问，先运行诊断脚本

🎯 成功标志
===========

设置成功后，您应该能够：
✅ 运行 git --version 显示版本
✅ 运行 git status 显示仓库状态
✅ 在GitHub上看到您的仓库
✅ 正常推送和拉取代码

如果以上都能正常工作，恭喜您设置成功！🎉

// Copyright 2010, <PERSON><PERSON>.  All rights reserved.
// http://code.google.com/p/muduo/
//
// Use of this source code is governed by a BSD-style license
// that can be found in the License file.

// Author: <PERSON><PERSON> (chenshuo at chenshuo dot com)
//
// This is a public header file, it must only include public header files.
// This is an internal header file, you should not include this.

#ifndef MUDUO_NET_BOILERPLATE_H
#define MUDUO_NET_BOILERPLATE_H

#include <muduo/base/noncopyable.h>

namespace muduo
{
namespace net
{

class BoilerPlate : noncopyable
{
 public:

 private:
};

}
}

#endif  // MUDUO_NET_BOILERPLATE_H

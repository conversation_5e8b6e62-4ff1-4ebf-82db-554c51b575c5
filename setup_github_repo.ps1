# GitHub Repository Setup PowerShell Script for Muduo

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "GitHub Repository Setup for Muduo" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if Git is installed
try {
    $gitVersion = git --version 2>$null
    Write-Host "✅ Git is installed: $gitVersion" -ForegroundColor Green
}
catch {
    Write-Host "❌ Error: Git is not installed." -ForegroundColor Red
    Write-Host "Please run install_git.bat first." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if we're in a Git repository
if (!(Test-Path ".git")) {
    Write-Host "❌ Error: This directory is not a Git repository." -ForegroundColor Red
    Write-Host "Please run init_git.bat first to initialize the repository." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Current repository status:" -ForegroundColor Yellow
git status --porcelain
Write-Host ""

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Step 1: GitHub Repository Creation" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "I'll guide you through creating a GitHub repository." -ForegroundColor Green
Write-Host "Please follow these steps:" -ForegroundColor Yellow
Write-Host ""
Write-Host "1. Open your web browser and go to: https://github.com/new" -ForegroundColor White
Write-Host "2. Fill in the repository details:" -ForegroundColor White
Write-Host "   - Repository name: muduo-network-library (or your preferred name)" -ForegroundColor White
Write-Host "   - Description: High-performance C++ network library based on Reactor pattern" -ForegroundColor White
Write-Host "   - Visibility: Public (recommended) or Private" -ForegroundColor White
Write-Host "   - DO NOT initialize with README, .gitignore, or license (we already have these)" -ForegroundColor Red
Write-Host "3. Click 'Create repository'" -ForegroundColor White
Write-Host ""

# Open GitHub new repository page
Write-Host "Opening GitHub repository creation page..." -ForegroundColor Green
Start-Process "https://github.com/new"
Write-Host ""

Write-Host "After creating the repository, you'll see a page with setup instructions." -ForegroundColor Cyan
Write-Host "Look for the section '...or push an existing repository from the command line'" -ForegroundColor Cyan
Write-Host ""
Read-Host "Press Enter after you've created the repository on GitHub"

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Step 2: Repository Information" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Please provide your GitHub repository information:" -ForegroundColor Yellow
Write-Host ""

$githubUsername = Read-Host "Enter your GitHub username"
$repoName = Read-Host "Enter your repository name (e.g., muduo-network-library)"

# Construct repository URLs
$repoUrl = "https://github.com/$githubUsername/$repoName.git"
$sshUrl = "**************:$githubUsername/$repoName.git"

Write-Host ""
Write-Host "Repository URLs:" -ForegroundColor Yellow
Write-Host "HTTPS: $repoUrl" -ForegroundColor White
Write-Host "SSH:   $sshUrl" -ForegroundColor White
Write-Host ""

Write-Host "Which connection method do you prefer?" -ForegroundColor Yellow
Write-Host "1. HTTPS (easier, requires username/password or token)" -ForegroundColor White
Write-Host "2. SSH (more secure, requires SSH key setup)" -ForegroundColor White
Write-Host ""

do {
    $connectionMethod = Read-Host "Enter choice (1 or 2)"
} while ($connectionMethod -ne "1" -and $connectionMethod -ne "2")

if ($connectionMethod -eq "2") {
    $remoteUrl = $sshUrl
    Write-Host ""
    Write-Host "You chose SSH. Make sure you have SSH keys set up with GitHub." -ForegroundColor Yellow
    Write-Host "If not, visit: https://docs.github.com/en/authentication/connecting-to-github-with-ssh" -ForegroundColor Cyan
    Write-Host ""
} else {
    $remoteUrl = $repoUrl
    Write-Host ""
    Write-Host "You chose HTTPS. You'll need to authenticate when pushing." -ForegroundColor Yellow
    Write-Host "For better security, consider using a Personal Access Token instead of password." -ForegroundColor Cyan
    Write-Host "Create one at: https://github.com/settings/tokens" -ForegroundColor Cyan
    Write-Host ""
}

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Step 3: Connecting Local Repository" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Adding remote origin..." -ForegroundColor Yellow
try {
    git remote add origin $remoteUrl 2>$null
    Write-Host "✅ Remote origin added successfully" -ForegroundColor Green
}
catch {
    Write-Host "Remote origin might already exist. Updating..." -ForegroundColor Yellow
    git remote set-url origin $remoteUrl
    Write-Host "✅ Remote origin updated" -ForegroundColor Green
}

Write-Host ""
Write-Host "Verifying remote configuration..." -ForegroundColor Yellow
git remote -v
Write-Host ""

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Step 4: Preparing for First Push" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Checking current branch..." -ForegroundColor Yellow
$currentBranch = git branch --show-current
Write-Host "Current branch: $currentBranch" -ForegroundColor White

if ($currentBranch -ne "main") {
    Write-Host "Renaming branch to 'main'..." -ForegroundColor Yellow
    git branch -M main
    Write-Host "✅ Branch renamed to 'main'" -ForegroundColor Green
}

Write-Host ""
Write-Host "Checking repository status..." -ForegroundColor Yellow
git status
Write-Host ""

# Check if there are uncommitted changes
$hasChanges = git diff-index --quiet HEAD -- 2>$null
if ($LASTEXITCODE -ne 0) {
    Write-Host "You have uncommitted changes." -ForegroundColor Yellow
    $commitChanges = Read-Host "Would you like to commit them now? (Y/N)"
    if ($commitChanges -eq "Y" -or $commitChanges -eq "y") {
        Write-Host ""
        $commitMessage = Read-Host "Enter commit message"
        git add .
        git commit -m $commitMessage
        Write-Host "✅ Changes committed" -ForegroundColor Green
    }
}

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Step 5: Pushing to GitHub" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Pushing to GitHub repository..." -ForegroundColor Yellow
Write-Host "This may take a moment and might require authentication..." -ForegroundColor Cyan
Write-Host ""

try {
    git push -u origin main
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "✅ SUCCESS! Repository pushed to GitHub successfully!" -ForegroundColor Green
        Write-Host ""
        Write-Host "Your repository is now available at:" -ForegroundColor Cyan
        Write-Host "https://github.com/$githubUsername/$repoName" -ForegroundColor White
        Write-Host ""
        Write-Host "Opening your repository in browser..." -ForegroundColor Green
        Start-Process "https://github.com/$githubUsername/$repoName"
        $pushSuccess = $true
    } else {
        throw "Push failed"
    }
}
catch {
    Write-Host ""
    Write-Host "❌ Push failed. This might be due to:" -ForegroundColor Red
    Write-Host "1. Authentication issues" -ForegroundColor Yellow
    Write-Host "2. Repository doesn't exist on GitHub" -ForegroundColor Yellow
    Write-Host "3. Network connectivity problems" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Common solutions:" -ForegroundColor Cyan
    Write-Host "- For HTTPS: Use personal access token instead of password" -ForegroundColor White
    Write-Host "- For SSH: Make sure SSH keys are properly configured" -ForegroundColor White
    Write-Host "- Verify repository exists on GitHub" -ForegroundColor White
    Write-Host ""
    $pushSuccess = $false
}

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Repository Setup Summary" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Local repository: $(Get-Location)" -ForegroundColor White
Write-Host "Remote repository: $remoteUrl" -ForegroundColor White
Write-Host "Branch: main" -ForegroundColor White
Write-Host ""
Write-Host "Useful Git commands for daily use:" -ForegroundColor Yellow
Write-Host "- git status           : Check repository status" -ForegroundColor White
Write-Host "- git add .            : Stage all changes" -ForegroundColor White
Write-Host "- git commit -m 'msg'  : Commit with message" -ForegroundColor White
Write-Host "- git push             : Push changes to GitHub" -ForegroundColor White
Write-Host "- git pull             : Pull latest changes from GitHub" -ForegroundColor White
Write-Host "- git log --oneline    : View commit history" -ForegroundColor White
Write-Host ""

if ($pushSuccess) {
    Write-Host "🎉 Your Muduo project is now on GitHub!" -ForegroundColor Green
    Write-Host "You can share the repository URL with others or continue development." -ForegroundColor Cyan
} else {
    Write-Host "⚠️  Setup completed with issues. Please resolve authentication problems." -ForegroundColor Yellow
}

Write-Host ""
Read-Host "Press Enter to exit"

# GitHub Repository Setup Guide

This guide will help you create a GitHub repository and connect your local Muduo project to it.

## 🚀 Quick Setup (Automated)

### Option 1: Run the Batch Script
```cmd
# Right-click and "Run as administrator"
setup_github_repo.bat
```

### Option 2: Run the PowerShell Script
```powershell
# Run in PowerShell as administrator
.\setup_github_repo.ps1
```

## 📋 Manual Setup Steps

If you prefer to do it manually, follow these detailed steps:

### Step 1: Create GitHub Account (if needed)

1. Go to https://github.com
2. Click "Sign up"
3. Follow the registration process
4. Verify your email address

### Step 2: Create New Repository on GitHub

1. **Go to GitHub**: https://github.com/new
2. **Fill in repository details**:
   - **Repository name**: `muduo-network-library` (or your preferred name)
   - **Description**: `High-performance C++ network library based on Reactor pattern`
   - **Visibility**: Choose Public (recommended) or Private
   - **Important**: ❌ DO NOT check any of these boxes:
     - Add a README file
     - Add .gitignore
     - Choose a license
   - (We already have these files in our local repository)
3. **Click "Create repository"**

### Step 3: Get Repository URL

After creating the repository, you'll see a setup page. Copy one of these URLs:

- **HTTPS**: `https://github.com/YOUR_USERNAME/REPO_NAME.git`
- **SSH**: `**************:YOUR_USERNAME/REPO_NAME.git`

### Step 4: Connect Local Repository

Open command prompt in your project directory and run:

```bash
# Add remote origin (replace with your actual URL)
git remote add origin https://github.com/YOUR_USERNAME/REPO_NAME.git

# Verify remote was added
git remote -v

# Rename branch to main (if not already)
git branch -M main

# Push to GitHub
git push -u origin main
```

## 🔐 Authentication Setup

### Option 1: HTTPS with Personal Access Token (Recommended)

1. **Create Personal Access Token**:
   - Go to: https://github.com/settings/tokens
   - Click "Generate new token" → "Generate new token (classic)"
   - Set expiration and select scopes:
     - ✅ `repo` (Full control of private repositories)
     - ✅ `workflow` (Update GitHub Action workflows)
   - Click "Generate token"
   - **Copy the token immediately** (you won't see it again)

2. **Use token when pushing**:
   - Username: Your GitHub username
   - Password: Use the personal access token (not your GitHub password)

### Option 2: SSH Keys (More Secure)

1. **Generate SSH Key** (if you don't have one):
   ```bash
   ssh-keygen -t ed25519 -C "<EMAIL>"
   ```

2. **Add SSH Key to SSH Agent**:
   ```bash
   eval "$(ssh-agent -s)"
   ssh-add ~/.ssh/id_ed25519
   ```

3. **Add SSH Key to GitHub**:
   - Copy your public key: `cat ~/.ssh/id_ed25519.pub`
   - Go to: https://github.com/settings/keys
   - Click "New SSH key"
   - Paste your public key and save

4. **Test SSH Connection**:
   ```bash
   ssh -T **************
   ```

## 📁 Repository Structure After Setup

```
muduo-network-library/
├── README.md                 # Project documentation
├── .gitignore               # Git ignore rules
├── CONTRIBUTING.md          # Contribution guidelines
├── GIT_SETUP_GUIDE.md       # Git setup instructions
├── GITHUB_SETUP_GUIDE.md    # This guide
├── muduo/                   # Source code
│   ├── base/               # Base utilities
│   └── net/                # Network components
├── examples/                # Usage examples
├── CMakeLists.txt          # Build configuration
└── ...                     # Other project files
```

## 🔧 Daily Git Workflow

### Making Changes
```bash
# Check status
git status

# Add changes
git add .
# or add specific files
git add file1.cpp file2.h

# Commit changes
git commit -m "feat: Add new feature description"

# Push to GitHub
git push
```

### Getting Updates
```bash
# Pull latest changes
git pull

# Check what changed
git log --oneline -10
```

### Branching
```bash
# Create and switch to new branch
git checkout -b feature/new-feature

# Push new branch to GitHub
git push -u origin feature/new-feature

# Switch back to main
git checkout main

# Merge feature branch
git merge feature/new-feature

# Delete merged branch
git branch -d feature/new-feature
```

## 🌟 GitHub Features to Explore

### 1. Issues and Project Management
- Create issues for bugs and feature requests
- Use labels to categorize issues
- Create milestones for releases

### 2. Pull Requests
- Create pull requests for code review
- Use branch protection rules
- Set up automated checks

### 3. GitHub Actions (CI/CD)
- Automated building and testing
- Code quality checks
- Deployment automation

### 4. Wiki and Documentation
- Create project wiki
- Host documentation with GitHub Pages

### 5. Releases
- Tag important versions
- Create release notes
- Distribute binaries

## 🛠️ Troubleshooting

### Common Issues

#### 1. Authentication Failed
```bash
# For HTTPS: Use personal access token
# Username: your_github_username
# Password: your_personal_access_token

# For SSH: Check SSH key setup
ssh -T **************
```

#### 2. Repository Already Exists
```bash
# If you need to change remote URL
git remote set-url origin NEW_URL
```

#### 3. Push Rejected
```bash
# If remote has changes you don't have locally
git pull --rebase origin main
git push
```

#### 4. Large Files
```bash
# For files larger than 100MB, use Git LFS
git lfs track "*.so"
git add .gitattributes
git add large_file.so
git commit -m "Add large file with LFS"
```

### Getting Help

- **Git Documentation**: https://git-scm.com/doc
- **GitHub Docs**: https://docs.github.com
- **GitHub Community**: https://github.community

## 📊 Repository Settings Recommendations

### Branch Protection Rules
1. Go to repository Settings → Branches
2. Add rule for `main` branch:
   - ✅ Require pull request reviews
   - ✅ Require status checks to pass
   - ✅ Require branches to be up to date

### Security Settings
1. Enable vulnerability alerts
2. Enable dependency security updates
3. Set up code scanning (if available)

### Collaboration Settings
1. Set repository visibility
2. Configure collaborator permissions
3. Set up teams (for organizations)

## 🎯 Next Steps After Setup

1. ✅ Repository created and connected
2. 🔄 Set up continuous integration
3. 🔄 Create project documentation
4. 🔄 Set up issue templates
5. 🔄 Configure branch protection
6. 🔄 Invite collaborators

## 📞 Support

If you encounter issues:

1. Check the error message carefully
2. Consult this guide and Git documentation
3. Search GitHub Community for similar issues
4. Create an issue in your repository for project-specific problems

---

**Congratulations! Your Muduo project is now on GitHub and ready for collaborative development!** 🎉

Remember to:
- Commit changes regularly
- Write descriptive commit messages
- Use branches for new features
- Keep your repository updated

Happy coding! 🚀

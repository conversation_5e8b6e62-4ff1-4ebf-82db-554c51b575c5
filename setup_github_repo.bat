@echo off
echo ========================================
echo GitHub Repository Setup for Muduo
echo ========================================
echo.

REM Check if Git is installed
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Git is not installed.
    echo Please run install_git.bat first.
    echo.
    pause
    exit /b 1
)

REM Check if we're in a Git repository
if not exist ".git" (
    echo Error: This directory is not a Git repository.
    echo Please run init_git.bat first to initialize the repository.
    echo.
    pause
    exit /b 1
)

echo Current repository status:
git status --porcelain
echo.

echo ========================================
echo Step 1: GitHub Repository Creation
echo ========================================
echo.
echo I'll guide you through creating a GitHub repository.
echo Please follow these steps:
echo.
echo 1. Open your web browser and go to: https://github.com/new
echo 2. Fill in the repository details:
echo    - Repository name: muduo-network-library (or your preferred name)
echo    - Description: High-performance C++ network library based on Reactor pattern
echo    - Visibility: Public (recommended) or Private
echo    - DO NOT initialize with README, .gitignore, or license (we already have these)
echo 3. Click "Create repository"
echo.

REM Open GitHub new repository page
echo Opening GitHub repository creation page...
start https://github.com/new
echo.

echo After creating the repository, you'll see a page with setup instructions.
echo Look for the section "...or push an existing repository from the command line"
echo.
pause

echo ========================================
echo Step 2: Repository Information
echo ========================================
echo.
echo Please provide your GitHub repository information:
echo.

set /p github_username="Enter your GitHub username: "
set /p repo_name="Enter your repository name (e.g., muduo-network-library): "

REM Construct repository URL
set repo_url=https://github.com/%github_username%/%repo_name%.git
set ssh_url=**************:%github_username%/%repo_name%.git

echo.
echo Repository URLs:
echo HTTPS: %repo_url%
echo SSH:   %ssh_url%
echo.

echo Which connection method do you prefer?
echo 1. HTTPS (easier, requires username/password or token)
echo 2. SSH (more secure, requires SSH key setup)
echo.
set /p connection_method="Enter choice (1 or 2): "

if "%connection_method%"=="2" (
    set remote_url=%ssh_url%
    echo.
    echo You chose SSH. Make sure you have SSH keys set up with GitHub.
    echo If not, visit: https://docs.github.com/en/authentication/connecting-to-github-with-ssh
    echo.
) else (
    set remote_url=%repo_url%
    echo.
    echo You chose HTTPS. You'll need to authenticate when pushing.
    echo.
)

echo ========================================
echo Step 3: Connecting Local Repository
echo ========================================
echo.

echo Adding remote origin...
git remote add origin %remote_url%
if %errorlevel% neq 0 (
    echo Remote origin might already exist. Updating...
    git remote set-url origin %remote_url%
)

echo.
echo Verifying remote configuration...
git remote -v
echo.

echo ========================================
echo Step 4: Preparing for First Push
echo ========================================
echo.

echo Checking current branch...
for /f "tokens=*" %%i in ('git branch --show-current') do set current_branch=%%i
echo Current branch: %current_branch%

if not "%current_branch%"=="main" (
    echo Renaming branch to 'main'...
    git branch -M main
)

echo.
echo Checking repository status...
git status
echo.

REM Check if there are uncommitted changes
git diff-index --quiet HEAD --
if %errorlevel% neq 0 (
    echo You have uncommitted changes. Would you like to commit them now?
    set /p commit_changes="Commit changes? (Y/N): "
    if /i "%commit_changes%"=="Y" (
        echo.
        set /p commit_message="Enter commit message: "
        git add .
        git commit -m "%commit_message%"
    )
)

echo ========================================
echo Step 5: Pushing to GitHub
echo ========================================
echo.

echo Pushing to GitHub repository...
echo This may take a moment and might require authentication...
echo.

git push -u origin main
if %errorlevel% equ 0 (
    echo.
    echo ✅ SUCCESS! Repository pushed to GitHub successfully!
    echo.
    echo Your repository is now available at:
    echo https://github.com/%github_username%/%repo_name%
    echo.
    echo Opening your repository in browser...
    start https://github.com/%github_username%/%repo_name%
) else (
    echo.
    echo ❌ Push failed. This might be due to:
    echo 1. Authentication issues
    echo 2. Repository doesn't exist on GitHub
    echo 3. Network connectivity problems
    echo.
    echo Please check the error message above and try again.
    echo.
    echo Common solutions:
    echo - For HTTPS: Use personal access token instead of password
    echo - For SSH: Make sure SSH keys are properly configured
    echo - Verify repository exists on GitHub
    echo.
)

echo ========================================
echo Repository Setup Summary
echo ========================================
echo.
echo Local repository: %CD%
echo Remote repository: %remote_url%
echo Branch: main
echo.
echo Useful Git commands for daily use:
echo - git status           : Check repository status
echo - git add .            : Stage all changes
echo - git commit -m "msg"  : Commit with message
echo - git push             : Push changes to GitHub
echo - git pull             : Pull latest changes from GitHub
echo - git log --oneline    : View commit history
echo.

if %errorlevel% equ 0 (
    echo 🎉 Your Muduo project is now on GitHub!
    echo You can share the repository URL with others or continue development.
) else (
    echo ⚠️  Setup completed with issues. Please resolve authentication problems.
)

echo.
pause

#include "../chargen/chargen.h"
#include "../daytime/daytime.h"
#include "../discard/discard.h"
#include "../echo/echo.h"
#include "../time/time.h"

#include <muduo/base/Logging.h>
#include <muduo/net/EventLoop.h>

#include <unistd.h>

using namespace muduo;
using namespace muduo::net;

int main()
{
  LOG_INFO << "pid = " << getpid();
  EventLoop loop;  // one loop shared by multiple servers

  ChargenServer chargenServer(&loop, InetAddress(2019));
  chargenServer.start();

  DaytimeServer daytimeServer(&loop, Inet<PERSON>dd<PERSON>(2013));
  daytimeServer.start();

  DiscardServer discardServer(&loop, <PERSON><PERSON><PERSON><PERSON><PERSON>(2009));
  discardServer.start();

  EchoServer echoServer(&loop, <PERSON>etAdd<PERSON>(2007));
  echoServer.start();

  TimeServer timeServer(&loop, <PERSON>etAdd<PERSON>(2037));
  timeServer.start();

  loop.loop();
}


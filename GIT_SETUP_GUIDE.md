# Git Setup Guide for Muduo Project

This guide will help you set up Git version control for the Muduo network library project.

## Prerequisites

### Install Git

#### Windows
1. Download Git from: https://git-scm.com/download/win
2. Run the installer and follow the setup wizard
3. Choose "Git from the command line and also from 3rd-party software"
4. Choose "Use the OpenSSL library"
5. Choose "Checkout Windows-style, commit Unix-style line endings"

#### Linux (Ubuntu/Debian)
```bash
sudo apt-get update
sudo apt-get install git
```

#### Linux (CentOS/RHEL)
```bash
sudo yum install git
# or for newer versions:
sudo dnf install git
```

#### macOS
```bash
# Using Homebrew
brew install git

# Or download from: https://git-scm.com/download/mac
```

## Quick Setup

### Option 1: Automated Setup (Recommended)

#### Windows
```cmd
# Run the batch script
init_git.bat
```

#### Linux/macOS
```bash
# Run the shell script
./init_git.sh
```

### Option 2: Manual Setup

#### 1. Initialize Git Repository
```bash
git init
```

#### 2. Configure Git (First time only)
```bash
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"
```

#### 3. Set Commit Message Template (Optional)
```bash
git config commit.template .gitmessage
```

#### 4. Add Files and Create Initial Commit
```bash
# Add all files
git add .

# Create initial commit
git commit -m "Initial commit: Add Muduo network library

- Add complete Muduo source code
- Add comprehensive .gitignore for C++ projects  
- Add detailed README.md with usage examples
- Set up CMake build system
- Include examples and tests"
```

## Working with Remote Repository

### 1. Create Remote Repository
Create a new repository on:
- GitHub: https://github.com/new
- GitLab: https://gitlab.com/projects/new
- Bitbucket: https://bitbucket.org/repo/create

### 2. Add Remote Origin
```bash
# Replace [URL] with your repository URL
git remote add origin [URL]

# Example:
git remote add origin https://github.com/username/muduo.git
```

### 3. Push to Remote Repository
```bash
# Push to main branch (first time)
git push -u origin main

# For subsequent pushes
git push
```

## Daily Git Workflow

### 1. Check Status
```bash
git status
```

### 2. Add Changes
```bash
# Add specific files
git add file1.cpp file2.h

# Add all changes
git add .

# Add all modified files (not new files)
git add -u
```

### 3. Commit Changes
```bash
# Commit with message
git commit -m "feat: Add new feature description"

# Commit with template (if configured)
git commit
```

### 4. Push Changes
```bash
git push
```

### 5. Pull Latest Changes
```bash
git pull
```

## Branching Strategy

### Create and Switch to New Branch
```bash
# Create and switch to new branch
git checkout -b feature/new-feature

# Or using newer syntax
git switch -c feature/new-feature
```

### Switch Between Branches
```bash
# Switch to existing branch
git checkout main
git switch main

# List all branches
git branch -a
```

### Merge Branch
```bash
# Switch to main branch
git checkout main

# Merge feature branch
git merge feature/new-feature

# Delete merged branch
git branch -d feature/new-feature
```

## Useful Git Commands

### View History
```bash
# View commit history
git log

# View compact history
git log --oneline

# View graphical history
git log --graph --oneline --all
```

### View Changes
```bash
# View unstaged changes
git diff

# View staged changes
git diff --cached

# View changes between commits
git diff HEAD~1 HEAD
```

### Undo Changes
```bash
# Unstage files
git reset HEAD file.cpp

# Discard unstaged changes
git checkout -- file.cpp

# Undo last commit (keep changes)
git reset --soft HEAD~1

# Undo last commit (discard changes)
git reset --hard HEAD~1
```

## Git Configuration for Muduo Project

### Recommended Global Settings
```bash
# Set default editor
git config --global core.editor "code --wait"  # VS Code
git config --global core.editor "vim"          # Vim

# Set default branch name
git config --global init.defaultBranch main

# Enable colored output
git config --global color.ui auto

# Set line ending handling
git config --global core.autocrlf input   # Linux/macOS
git config --global core.autocrlf true    # Windows
```

### Project-Specific Settings
```bash
# Set commit message template
git config commit.template .gitmessage

# Set up aliases
git config alias.st status
git config alias.co checkout
git config alias.br branch
git config alias.ci commit
```

## File Structure After Git Setup

```
muduo-master/
├── .git/                 # Git repository data
├── .gitignore           # Files to ignore
├── .gitmessage          # Commit message template
├── README.md            # Project documentation
├── GIT_SETUP_GUIDE.md   # This guide
├── init_git.bat         # Windows setup script
├── init_git.sh          # Linux/macOS setup script
├── muduo/               # Source code
├── examples/            # Example code
└── ...                  # Other project files
```

## Troubleshooting

### Common Issues

#### 1. Git not found
- Make sure Git is installed and in your PATH
- Restart your terminal/command prompt after installation

#### 2. Permission denied (publickey)
- Set up SSH keys: https://docs.github.com/en/authentication/connecting-to-github-with-ssh

#### 3. Line ending issues
- Configure `core.autocrlf` appropriately for your platform

#### 4. Large files
- Consider using Git LFS for large binary files

### Getting Help
```bash
# Get help for specific command
git help <command>
git <command> --help

# Examples
git help commit
git status --help
```

## Next Steps

1. ✅ Initialize Git repository
2. ✅ Create initial commit
3. 🔄 Create remote repository
4. 🔄 Push to remote
5. 🔄 Set up branching strategy
6. 🔄 Configure CI/CD (optional)

Happy coding with Git and Muduo! 🚀

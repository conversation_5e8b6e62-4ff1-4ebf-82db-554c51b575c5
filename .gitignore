# Compiled Object files
*.slo
*.lo
*.o
*.obj

# Precompiled Headers
*.gch
*.pch

# Compiled Dynamic libraries
*.so
*.dylib
*.dll

# Fortran module files
*.mod
*.smod

# Compiled Static libraries
*.lai
*.la
*.a
*.lib

# Executables
*.exe
*.out
*.app

# Build directories
build/
build-*/
cmake-build-*/
CMakeFiles/
CMakeCache.txt
cmake_install.cmake
Makefile
*.cmake
!CMakeLists.txt

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~
compile_commands.json

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log

# Core dumps
core
core.*

# Temporary files
*.tmp
*.temp

# Backup files
*.bak
*.backup

# Generated documentation
doc/html/
doc/latex/

# Test results
test_results/
*.gcov
*.gcda
*.gcno

# Profiling data
*.prof
gmon.out

# Valgrind output
*.valgrind

# Coverage reports
coverage/
*.coverage

# Package files
*.tar.gz
*.zip
*.rar

# Local configuration
local.properties
config.local

# Binary output directories
bin/
lib/

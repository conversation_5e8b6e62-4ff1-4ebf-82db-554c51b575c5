# Git Auto-Installer PowerShell Script
# Run this script as Administrator

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Git Auto-Installer for Windows" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "This script requires administrator privileges." -ForegroundColor Red
    Write-Host "Please right-click PowerShell and select 'Run as administrator'" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

# Function to test if Git is installed
function Test-GitInstalled {
    try {
        $null = git --version 2>$null
        return $true
    }
    catch {
        return $false
    }
}

# Check if Git is already installed
Write-Host "[1/5] Checking if Git is already installed..." -ForegroundColor Yellow
if (Test-GitInstalled) {
    Write-Host "Git is already installed!" -ForegroundColor Green
    git --version
    Write-Host ""
    $continue = Read-Host "Do you want to continue with repository setup? (Y/N)"
    if ($continue -eq "Y" -or $continue -eq "y") {
        goto SetupRepo
    } else {
        Write-Host "Exiting..." -ForegroundColor Yellow
        Read-Host "Press Enter to exit"
        exit 0
    }
}

Write-Host "Git is not installed. Starting installation..." -ForegroundColor Yellow
Write-Host ""

# Try winget first (Windows 10/11)
Write-Host "[2/5] Trying winget installation..." -ForegroundColor Yellow
try {
    $wingetVersion = winget --version 2>$null
    if ($wingetVersion) {
        Write-Host "Found winget! Installing Git..." -ForegroundColor Green
        winget install --id Git.Git -e --source winget --silent --accept-package-agreements --accept-source-agreements
        
        # Wait a moment for installation to complete
        Start-Sleep -Seconds 10
        
        # Refresh environment
        $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
        
        if (Test-GitInstalled) {
            Write-Host "✅ Git installed successfully via winget!" -ForegroundColor Green
            goto VerifyInstallation
        }
    }
}
catch {
    Write-Host "winget not available or failed. Trying Chocolatey..." -ForegroundColor Yellow
}

# Try Chocolatey
Write-Host "[3/5] Trying Chocolatey installation..." -ForegroundColor Yellow
try {
    $chocoVersion = choco --version 2>$null
    if ($chocoVersion) {
        Write-Host "Found Chocolatey! Installing Git..." -ForegroundColor Green
        choco install git -y
        
        # Refresh environment
        refreshenv
        
        if (Test-GitInstalled) {
            Write-Host "✅ Git installed successfully via Chocolatey!" -ForegroundColor Green
            goto VerifyInstallation
        }
    }
}
catch {
    Write-Host "Chocolatey not available. Trying direct download..." -ForegroundColor Yellow
}

# Direct download and install
Write-Host "[4/5] Downloading Git installer directly..." -ForegroundColor Yellow
Write-Host "This may take a few minutes depending on your internet connection..." -ForegroundColor Cyan

try {
    # Create temp directory
    $tempDir = "$env:TEMP\git_installer"
    if (!(Test-Path $tempDir)) {
        New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
    }
    
    # Download Git installer
    $installerPath = "$tempDir\git-installer.exe"
    $downloadUrl = "https://github.com/git-for-windows/git/releases/latest/download/Git-********-64-bit.exe"
    
    Write-Host "Downloading from: $downloadUrl" -ForegroundColor Cyan
    [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12
    Invoke-WebRequest -Uri $downloadUrl -OutFile $installerPath -UseBasicParsing
    
    if (Test-Path $installerPath) {
        Write-Host "Download completed. Installing Git..." -ForegroundColor Green
        
        # Run installer silently
        $installArgs = "/VERYSILENT /NORESTART /NOCANCEL /SP- /CLOSEAPPLICATIONS /RESTARTAPPLICATIONS"
        Start-Process -FilePath $installerPath -ArgumentList $installArgs -Wait
        
        # Clean up
        Remove-Item -Path $tempDir -Recurse -Force -ErrorAction SilentlyContinue
        
        # Refresh environment
        $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
        
        Write-Host "Installation completed. Verifying..." -ForegroundColor Green
    } else {
        throw "Failed to download installer"
    }
}
catch {
    Write-Host "❌ Automatic installation failed." -ForegroundColor Red
    Write-Host "Please manually download and install Git from: https://git-scm.com/download/win" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

:VerifyInstallation
Write-Host "[5/5] Verifying Git installation..." -ForegroundColor Yellow

# Wait a moment and test
Start-Sleep -Seconds 3
if (Test-GitInstalled) {
    Write-Host "✅ Git installed successfully!" -ForegroundColor Green
    git --version
    Write-Host ""
} else {
    Write-Host "❌ Git installation verification failed." -ForegroundColor Red
    Write-Host "Please restart PowerShell and try again." -ForegroundColor Yellow
    Write-Host "If the problem persists, manually install from: https://git-scm.com/download/win" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

:SetupRepo
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Setting up Git repository for Muduo" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Configure Git
Write-Host "Configuring Git..." -ForegroundColor Yellow
$gitName = Read-Host "Enter your name for Git commits"
$gitEmail = Read-Host "Enter your email for Git commits"

git config --global user.name "$gitName"
git config --global user.email "$gitEmail"
git config --global init.defaultBranch main
git config --global core.autocrlf true

Write-Host "Git configuration completed!" -ForegroundColor Green
Write-Host ""

# Initialize repository
Write-Host "Initializing Git repository..." -ForegroundColor Yellow
git init

# Add all files
Write-Host "Adding files to repository..." -ForegroundColor Yellow
git add .

# Create initial commit
Write-Host "Creating initial commit..." -ForegroundColor Yellow
$commitMessage = @"
Initial commit: Add Muduo network library

- Add complete Muduo source code with base and net modules
- Add comprehensive .gitignore for C++ projects
- Add detailed README.md with usage examples and architecture
- Set up CMake build system with proper dependencies
- Include extensive examples and test suites
- Add Git setup and contribution guidelines
"@

git commit -m $commitMessage

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "🎉 SUCCESS! Git setup completed!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Repository initialized with initial commit." -ForegroundColor Cyan
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Create a repository on GitHub/GitLab/Bitbucket" -ForegroundColor White
Write-Host "2. Add remote origin: git remote add origin [YOUR_REPO_URL]" -ForegroundColor White
Write-Host "3. Push to remote: git push -u origin main" -ForegroundColor White
Write-Host ""
Write-Host "Git commands you can use now:" -ForegroundColor Yellow
Write-Host "- git status          : Check repository status" -ForegroundColor White
Write-Host "- git add [file]      : Stage files for commit" -ForegroundColor White
Write-Host "- git commit -m 'msg' : Commit changes" -ForegroundColor White
Write-Host "- git push            : Push to remote repository" -ForegroundColor White
Write-Host "- git pull            : Pull latest changes" -ForegroundColor White
Write-Host ""
Write-Host "Repository location: $(Get-Location)" -ForegroundColor Cyan
Write-Host ""
Read-Host "Press Enter to exit"

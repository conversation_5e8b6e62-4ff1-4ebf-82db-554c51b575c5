@echo off
echo ========================================
echo Simple Muduo Project Diagnostics
echo ========================================
echo.
echo Current directory: %CD%
echo.

echo Checking project files...
if exist muduo echo [OK] muduo directory found
if not exist muduo echo [MISSING] muduo directory not found

if exist examples echo [OK] examples directory found  
if not exist examples echo [MISSING] examples directory not found

if exist CMakeLists.txt echo [OK] CMakeLists.txt found
if not exist CMakeLists.txt echo [MISSING] CMakeLists.txt not found

echo.
echo Checking Git installation...
git --version 2>nul
if errorlevel 1 (
    echo [MISSING] Git is not installed or not in PATH
    echo Please install Git from: https://git-scm.com/download/win
) else (
    echo [OK] Git is installed
)

echo.
echo Checking Git repository...
if exist .git (
    echo [OK] Git repository initialized
) else (
    echo [MISSING] Git repository not initialized
    echo Run: git init
)

echo.
echo ========================================
echo Diagnosis Complete
echo ========================================
echo.
echo What to do next:
echo.
echo If Git is missing:
echo   1. Download from: https://git-scm.com/download/win
echo   2. Install with default settings
echo   3. Restart command prompt
echo.
echo If Git repository is missing:
echo   1. Run: git init
echo   2. Run: git add .
echo   3. Run: git commit -m "Initial commit"
echo.
echo If everything looks OK:
echo   1. Try running setup_github_repo.bat again
echo.
pause

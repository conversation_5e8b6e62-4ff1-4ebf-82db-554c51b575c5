# Contributing to Muduo

Thank you for your interest in contributing to the Muduo network library! This document provides guidelines for contributing to the project.

## Getting Started

### Prerequisites

- Linux development environment (Ubuntu, CentOS, etc.)
- GCC >= 4.7 or Clang >= 3.5
- CMake >= 2.6
- Git for version control
- Basic knowledge of C++11 and network programming

### Setting Up Development Environment

1. **Clone the repository**
   ```bash
   git clone [your-fork-url]
   cd muduo
   ```

2. **Build the project**
   ```bash
   ./build.sh
   # or
   mkdir build && cd build
   cmake .. && make
   ```

3. **Run tests**
   ```bash
   cd build
   make test
   ```

## Development Workflow

### 1. Create a Feature Branch

```bash
git checkout -b feature/your-feature-name
```

### 2. Make Changes

- Follow the existing code style
- Add tests for new functionality
- Update documentation as needed
- Ensure all tests pass

### 3. Commit Changes

Use descriptive commit messages following this format:

```
<type>: <subject>

<body>

<footer>
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `perf`: Performance improvements
- `test`: Test additions/modifications
- `build`: Build system changes

**Example:**
```
feat: Add SSL/TLS support to TcpServer

- Implement SSL context management
- Add SSL handshake handling
- Update examples with SSL usage
- Add comprehensive SSL tests

Closes #123
```

### 4. Push and Create Pull Request

```bash
git push origin feature/your-feature-name
```

Then create a pull request on the repository platform.

## Code Style Guidelines

### C++ Style

Follow Google C++ Style Guide with these specific rules:

#### Naming Conventions
- **Classes**: PascalCase (`TcpServer`, `EventLoop`)
- **Functions**: camelCase (`handleRead`, `onConnection`)
- **Variables**: camelCase (`connectionCallback_`, `localAddr`)
- **Constants**: kPascalCase (`kMaxBufferSize`)
- **Macros**: UPPER_CASE (`MUDUO_NET_BUFFER_H`)

#### File Organization
```cpp
// Copyright header
// Brief description

#ifndef MUDUO_NET_FILENAME_H
#define MUDUO_NET_FILENAME_H

// System includes
#include <vector>
#include <memory>

// Third-party includes
#include <boost/any.hpp>

// Project includes
#include <muduo/base/noncopyable.h>

namespace muduo {
namespace net {

class ClassName : noncopyable {
 public:
  // Public interface

 private:
  // Private implementation
};

}  // namespace net
}  // namespace muduo

#endif  // MUDUO_NET_FILENAME_H
```

#### Code Formatting
- Use 2 spaces for indentation
- Maximum line length: 80 characters
- Use clang-format for consistent formatting

### Documentation

- Document all public APIs
- Use Doxygen-style comments
- Provide usage examples for complex features
- Update README.md for significant changes

## Testing Guidelines

### Unit Tests

- Write tests for all new functionality
- Use Google Test framework
- Place tests in `tests/` subdirectories
- Test file naming: `ClassName_test.cc`

### Integration Tests

- Test complete workflows
- Include performance benchmarks
- Test error conditions and edge cases

### Example Test Structure

```cpp
#include <muduo/net/TcpServer.h>
#include <gtest/gtest.h>

class TcpServerTest : public ::testing::Test {
 protected:
  void SetUp() override {
    // Setup test environment
  }

  void TearDown() override {
    // Cleanup
  }
};

TEST_F(TcpServerTest, BasicFunctionality) {
  // Test implementation
  EXPECT_TRUE(condition);
  ASSERT_EQ(expected, actual);
}
```

## Performance Considerations

- Profile code changes for performance impact
- Avoid unnecessary memory allocations
- Use move semantics where appropriate
- Consider cache locality in data structures
- Benchmark network-intensive code

## Documentation Standards

### Code Comments

```cpp
/// Brief description of the class/function
///
/// Detailed description explaining:
/// - What it does
/// - How to use it
/// - Important considerations
/// - Thread safety guarantees
///
/// @param param1 Description of parameter
/// @param param2 Description of parameter
/// @return Description of return value
/// @throws ExceptionType When this exception is thrown
class ExampleClass {
  // Implementation
};
```

### README Updates

- Update feature lists
- Add new examples
- Update build instructions if needed
- Document breaking changes

## Submitting Changes

### Pull Request Checklist

- [ ] Code follows style guidelines
- [ ] All tests pass
- [ ] New tests added for new functionality
- [ ] Documentation updated
- [ ] No compiler warnings
- [ ] Performance impact considered
- [ ] Breaking changes documented

### Pull Request Description

Include:
- Summary of changes
- Motivation and context
- Testing performed
- Screenshots (if UI changes)
- Related issues

## Review Process

1. **Automated Checks**: CI/CD pipeline runs tests
2. **Code Review**: Maintainers review code quality
3. **Testing**: Verify functionality and performance
4. **Documentation**: Check documentation completeness
5. **Approval**: Maintainer approval required for merge

## Community Guidelines

### Communication

- Be respectful and constructive
- Ask questions if unclear about requirements
- Provide helpful feedback in reviews
- Share knowledge and help others

### Issue Reporting

When reporting bugs:
- Use clear, descriptive titles
- Provide minimal reproduction case
- Include system information
- Attach relevant logs/stack traces

### Feature Requests

- Explain the use case
- Provide implementation suggestions
- Consider backward compatibility
- Discuss performance implications

## Resources

### Learning Materials

- [Muduo Network Programming Examples](examples/)
- [Linux Network Programming](http://www.unpbook.com/)
- [C++ Concurrency in Action](https://www.manning.com/books/c-plus-plus-concurrency-in-action)

### Tools

- **Debugging**: gdb, valgrind
- **Profiling**: perf, gperftools
- **Static Analysis**: clang-static-analyzer, cppcheck
- **Formatting**: clang-format

### Getting Help

- Check existing issues and documentation
- Ask questions in discussions
- Join community forums
- Contact maintainers for complex issues

## License

By contributing to Muduo, you agree that your contributions will be licensed under the same BSD-style license as the project.

---

Thank you for contributing to Muduo! Your efforts help make this library better for everyone. 🚀

#!/bin/bash
# Pre-commit hook example for Muduo project
# Copy this file to .git/hooks/pre-commit and make it executable

echo "Running pre-commit checks..."

# Check for trailing whitespace
if git diff --cached --check; then
    echo "✅ No trailing whitespace found"
else
    echo "❌ Found trailing whitespace. Please fix before committing."
    exit 1
fi

# Check for large files (>10MB)
large_files=$(git diff --cached --name-only | xargs -I {} find {} -size +10M 2>/dev/null)
if [ -n "$large_files" ]; then
    echo "❌ Large files detected (>10MB):"
    echo "$large_files"
    echo "Consider using Git LFS for large files."
    exit 1
else
    echo "✅ No large files detected"
fi

# Check for TODO/FIXME comments in staged files
staged_files=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.(cpp|cc|h|hpp)$')
if [ -n "$staged_files" ]; then
    todo_count=$(grep -n -E "(TODO|FIXME|XXX)" $staged_files | wc -l)
    if [ $todo_count -gt 0 ]; then
        echo "⚠️  Found $todo_count TODO/FIXME comments in staged files:"
        grep -n -E "(TODO|FIXME|XXX)" $staged_files
        echo "Consider addressing these before committing."
        # Don't exit - just warn
    fi
fi

# Check C++ file formatting (if clang-format is available)
if command -v clang-format >/dev/null 2>&1; then
    echo "🔍 Checking C++ code formatting..."
    cpp_files=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.(cpp|cc|h|hpp)$')
    
    if [ -n "$cpp_files" ]; then
        for file in $cpp_files; do
            if [ -f "$file" ]; then
                # Check if file needs formatting
                if ! clang-format --dry-run --Werror "$file" >/dev/null 2>&1; then
                    echo "❌ $file needs formatting. Run: clang-format -i $file"
                    exit 1
                fi
            fi
        done
        echo "✅ C++ code formatting looks good"
    fi
else
    echo "ℹ️  clang-format not found, skipping format check"
fi

# Check for common C++ issues
echo "🔍 Checking for common C++ issues..."
cpp_files=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.(cpp|cc|h|hpp)$')

if [ -n "$cpp_files" ]; then
    # Check for missing include guards in header files
    header_files=$(echo "$cpp_files" | grep -E '\.(h|hpp)$')
    for file in $header_files; do
        if [ -f "$file" ]; then
            if ! grep -q "#ifndef\|#pragma once" "$file"; then
                echo "⚠️  $file might be missing include guards"
            fi
        fi
    done
    
    # Check for potential memory leaks (basic check)
    for file in $cpp_files; do
        if [ -f "$file" ]; then
            new_count=$(grep -c "\bnew\b" "$file" || true)
            delete_count=$(grep -c "\bdelete\b" "$file" || true)
            if [ $new_count -gt 0 ] && [ $delete_count -eq 0 ]; then
                echo "⚠️  $file has 'new' but no 'delete' - check for memory leaks"
            fi
        fi
    done
fi

echo "✅ Pre-commit checks completed successfully!"
exit 0

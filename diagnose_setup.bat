@echo off
echo ========================================
echo Muduo Project Setup Diagnostics
echo ========================================
echo.
echo This script will check your system and help identify any issues.
echo.

REM Get current directory
echo [1/8] Current Directory Check
echo =====================================
echo Current directory: %CD%
echo.

REM Check if we're in the right directory
echo [2/8] Project Structure Check
echo =====================================
if exist "muduo" (
    echo ✅ muduo/ directory found
) else (
    echo ❌ muduo/ directory NOT found
    echo    This might not be the correct project directory
)

if exist "examples" (
    echo ✅ examples/ directory found
) else (
    echo ❌ examples/ directory NOT found
)

if exist "CMakeLists.txt" (
    echo ✅ CMakeLists.txt found
) else (
    echo ❌ CMakeLists.txt NOT found
)

if exist "README.md" (
    echo ✅ README.md found
) else (
    echo ❌ README.md NOT found
)
echo.

REM Check Git installation
echo [3/8] Git Installation Check
echo =====================================
git --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Git is installed and accessible
    git --version
) else (
    echo ❌ Git is NOT installed or not in PATH
    echo    Please install Git from: https://git-scm.com/download/win
)
echo.

REM Check if Git repository is initialized
echo [4/8] Git Repository Check
echo =====================================
if exist ".git" (
    echo ✅ Git repository is initialized
    if exist ".git\config" (
        echo ✅ Git configuration file exists
    ) else (
        echo ⚠️  Git configuration might be incomplete
    )
) else (
    echo ❌ Git repository is NOT initialized
    echo    Run 'git init' to initialize the repository
)
echo.

REM Check Git configuration
echo [5/8] Git Configuration Check
echo =====================================
git config user.name >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Git user name is configured
    for /f "tokens=*" %%i in ('git config user.name 2^>nul') do echo    Name: %%i
) else (
    echo ❌ Git user name is NOT configured
    echo    Run: git config --global user.name "Your Name"
)

git config user.email >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Git user email is configured
    for /f "tokens=*" %%i in ('git config user.email 2^>nul') do echo    Email: %%i
) else (
    echo ❌ Git user email is NOT configured
    echo    Run: git config --global user.email "<EMAIL>"
)
echo.

REM Check repository status
echo [6/8] Repository Status Check
echo =====================================
if exist ".git" (
    git status >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ Repository status is accessible
        echo Repository status:
        git status --porcelain
        if %errorlevel% equ 0 (
            echo ✅ Repository status command successful
        ) else (
            echo ⚠️  Repository might have issues
        )
    ) else (
        echo ❌ Cannot access repository status
    )
) else (
    echo ❌ No Git repository to check
)
echo.

REM Check for remote repositories
echo [7/8] Remote Repository Check
echo =====================================
if exist ".git" (
    git remote -v >nul 2>&1
    if %errorlevel% equ 0 (
        for /f %%i in ('git remote 2^>nul') do (
            echo ✅ Remote repository configured: %%i
            git remote get-url %%i 2>nul
        )
        git remote >nul 2>&1
        if %errorlevel% neq 0 (
            echo ⚠️  No remote repositories configured
            echo    This is normal for a new local repository
        )
    ) else (
        echo ⚠️  Cannot check remote repositories
    )
) else (
    echo ❌ No Git repository to check remotes
)
echo.

REM Check network connectivity
echo [8/8] Network Connectivity Check
echo =====================================
echo Testing GitHub connectivity...
ping -n 1 github.com >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Can reach GitHub (github.com)
) else (
    echo ❌ Cannot reach GitHub
    echo    Check your internet connection
)
echo.

REM Summary and recommendations
echo ========================================
echo DIAGNOSIS SUMMARY
echo ========================================
echo.

REM Determine what needs to be done
set needs_git=0
set needs_init=0
set needs_config=0

git --version >nul 2>&1
if %errorlevel% neq 0 set needs_git=1

if not exist ".git" set needs_init=1

git config user.name >nul 2>&1
if %errorlevel% neq 0 set needs_config=1

git config user.email >nul 2>&1
if %errorlevel% neq 0 set needs_config=1

if %needs_git% equ 1 (
    echo 🔧 ACTION REQUIRED: Install Git
    echo    1. Run: install_git.bat (as administrator)
    echo    2. Or manually install from: https://git-scm.com/download/win
    echo.
)

if %needs_init% equ 1 (
    echo 🔧 ACTION REQUIRED: Initialize Git repository
    echo    1. Run: init_git.bat (as administrator)
    echo    2. Or manually run: git init, git add ., git commit -m "Initial commit"
    echo.
)

if %needs_config% equ 1 (
    echo 🔧 ACTION REQUIRED: Configure Git user information
    echo    1. Run: git config --global user.name "Your Name"
    echo    2. Run: git config --global user.email "<EMAIL>"
    echo.
)

if %needs_git% equ 0 if %needs_init% equ 0 if %needs_config% equ 0 (
    echo ✅ READY: Your system appears to be ready for GitHub setup!
    echo    You can now run: setup_github_repo.bat
    echo.
)

echo ========================================
echo NEXT STEPS
echo ========================================
echo.
echo Based on the diagnosis above:
echo.
if %needs_git% equ 1 (
    echo 1. Install Git first: install_git.bat
    echo 2. Initialize repository: init_git.bat  
    echo 3. Set up GitHub: setup_github_repo.bat
) else if %needs_init% equ 1 (
    echo 1. Initialize repository: init_git.bat
    echo 2. Set up GitHub: setup_github_repo.bat
) else (
    echo 1. Set up GitHub: setup_github_repo.bat
    echo 2. Start developing with Git!
)
echo.
echo For detailed help, see:
echo - GIT_SETUP_GUIDE.md
echo - GITHUB_SETUP_GUIDE.md
echo - 一键安装Git.txt
echo.
pause

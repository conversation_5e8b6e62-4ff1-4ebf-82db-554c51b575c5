@echo off
echo ========================================
echo Git Auto-Installer for Windows
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo This script requires administrator privileges.
    echo Please right-click and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo [1/5] Checking if Git is already installed...
git --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Git is already installed!
    git --version
    echo.
    echo Do you want to continue with repository setup? (Y/N)
    set /p continue="Enter choice: "
    if /i "%continue%"=="Y" (
        goto :setup_repo
    ) else (
        echo Exiting...
        pause
        exit /b 0
    )
)

echo Git is not installed. Starting installation...
echo.

REM Check if Chocolatey is available
echo [2/5] Checking for package managers...
choco --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Found Chocolatey! Installing Git via Chocolatey...
    choco install git -y
    if %errorlevel% equ 0 (
        echo Git installed successfully via Chocolatey!
        goto :verify_installation
    ) else (
        echo Chocolatey installation failed. Trying alternative method...
    )
)

REM Check if winget is available (Windows 10/11)
echo Checking for winget...
winget --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Found winget! Installing Git via winget...
    winget install --id Git.Git -e --source winget
    if %errorlevel% equ 0 (
        echo Git installed successfully via winget!
        goto :verify_installation
    ) else (
        echo winget installation failed. Trying alternative method...
    )
)

REM Download and install Git manually
echo [3/5] Downloading Git installer...
echo This may take a few minutes depending on your internet connection...

REM Create temp directory
if not exist "%TEMP%\git_installer" mkdir "%TEMP%\git_installer"
cd /d "%TEMP%\git_installer"

REM Download Git installer using PowerShell
echo Downloading Git installer...
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://github.com/git-for-windows/git/releases/latest/download/Git-2.42.0.2-64-bit.exe' -OutFile 'git-installer.exe' -UseBasicParsing}"

if not exist "git-installer.exe" (
    echo Failed to download Git installer.
    echo Please manually download from: https://git-scm.com/download/win
    echo.
    pause
    exit /b 1
)

echo [4/5] Installing Git...
echo Running silent installation with recommended settings...

REM Run installer with silent parameters
git-installer.exe /VERYSILENT /NORESTART /NOCANCEL /SP- /CLOSEAPPLICATIONS /RESTARTAPPLICATIONS /COMPONENTS="icons,ext\reg\shellhere,assoc,assoc_sh" /TASKS="desktopicon,quicklaunchicon,addcontextmenufiles,addcontextmenufolders,associateshfiles"

REM Wait for installation to complete
timeout /t 10 /nobreak >nul

REM Clean up
cd /d "%~dp0"
rmdir /s /q "%TEMP%\git_installer" 2>nul

:verify_installation
echo [5/5] Verifying Git installation...

REM Refresh environment variables
call refreshenv >nul 2>&1

REM Test Git installation
timeout /t 3 /nobreak >nul
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Git installation verification failed.
    echo Please restart your command prompt and try again.
    echo If the problem persists, manually install from: https://git-scm.com/download/win
    echo.
    pause
    exit /b 1
)

echo ✅ Git installed successfully!
git --version
echo.

:setup_repo
echo ========================================
echo Setting up Git repository for Muduo
echo ========================================
echo.

REM Configure Git (first time setup)
echo Configuring Git...
set /p git_name="Enter your name for Git commits: "
set /p git_email="Enter your email for Git commits: "

git config --global user.name "%git_name%"
git config --global user.email "%git_email%"

REM Set up some useful Git configurations
git config --global init.defaultBranch main
git config --global core.autocrlf true
git config --global core.editor "notepad"

echo Git configuration completed!
echo.

REM Initialize repository
echo Initializing Git repository...
git init

REM Add all files
echo Adding files to repository...
git add .

REM Create initial commit
echo Creating initial commit...
git commit -m "Initial commit: Add Muduo network library

- Add complete Muduo source code with base and net modules
- Add comprehensive .gitignore for C++ projects
- Add detailed README.md with usage examples and architecture
- Set up CMake build system with proper dependencies
- Include extensive examples and test suites
- Add Git setup and contribution guidelines"

echo.
echo ========================================
echo 🎉 SUCCESS! Git setup completed!
echo ========================================
echo.
echo Repository initialized with initial commit.
echo.
echo Next steps:
echo 1. Create a repository on GitHub/GitLab/Bitbucket
echo 2. Add remote origin: git remote add origin [YOUR_REPO_URL]
echo 3. Push to remote: git push -u origin main
echo.
echo Git commands you can use now:
echo - git status          : Check repository status
echo - git add [file]      : Stage files for commit
echo - git commit -m "msg" : Commit changes
echo - git push            : Push to remote repository
echo - git pull            : Pull latest changes
echo.
echo Repository location: %CD%
echo.
pause

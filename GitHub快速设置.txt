🚀 GitHub 快速设置指南
======================

📋 自动化设置（推荐）
====================

1. 右键点击 "setup_github_repo.bat"
2. 选择 "以管理员身份运行"
3. 按照屏幕提示操作
4. 脚本会自动打开GitHub创建页面
5. 完成后自动连接和推送

📋 手动设置步骤
================

第一步：创建GitHub仓库
---------------------
1. 访问：https://github.com/new
2. 仓库名：muduo-network-library
3. 描述：High-performance C++ network library based on Reactor pattern
4. 选择 Public 或 Private
5. ❌ 不要勾选 README、.gitignore、license
6. 点击 "Create repository"

第二步：连接本地仓库
-------------------
复制GitHub给出的命令，或使用以下命令：

git remote add origin https://github.com/用户名/仓库名.git
git branch -M main
git push -u origin main

🔐 认证设置
============

方法1：个人访问令牌（推荐）
-------------------------
1. 访问：https://github.com/settings/tokens
2. 点击 "Generate new token (classic)"
3. 选择权限：repo, workflow
4. 复制生成的令牌
5. 推送时使用令牌作为密码

方法2：SSH密钥
--------------
1. 生成密钥：ssh-keygen -t ed25519 -C "邮箱"
2. 复制公钥：cat ~/.ssh/id_ed25519.pub
3. 添加到GitHub：https://github.com/settings/keys
4. 使用SSH URL：**************:用户名/仓库名.git

🎯 验证设置
============

设置完成后，运行以下命令验证：

git remote -v
git status
git log --oneline

应该能看到：
- 远程仓库URL
- 干净的工作目录
- 提交历史

📱 常用命令
============

日常使用：
git status          # 检查状态
git add .           # 添加所有更改
git commit -m "消息" # 提交更改
git push            # 推送到GitHub
git pull            # 拉取最新更改

分支操作：
git checkout -b 分支名    # 创建并切换分支
git checkout main        # 切换到主分支
git merge 分支名         # 合并分支

🔧 故障排除
============

问题1：认证失败
解决：使用个人访问令牌替代密码

问题2：推送被拒绝
解决：git pull --rebase origin main

问题3：仓库不存在
解决：检查仓库URL是否正确

问题4：权限不足
解决：检查仓库权限设置

📞 获取帮助
============

- 查看详细指南：GITHUB_SETUP_GUIDE.md
- GitHub文档：https://docs.github.com
- Git文档：https://git-scm.com/doc

🎉 完成后的下一步
==================

1. ✅ 仓库已创建并连接
2. 🔄 邀请协作者
3. 🔄 设置分支保护
4. 🔄 创建Issues模板
5. 🔄 设置CI/CD

恭喜！您的Muduo项目现在已经在GitHub上了！🎊

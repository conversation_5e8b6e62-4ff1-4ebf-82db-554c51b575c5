// Use of this source code is governed by a BSD-style license
// that can be found in the License file.
//
// Author: <PERSON><PERSON> (chenshuo at chenshuo dot com)

#include <muduo/base/Exception.h>
#include <muduo/base/CurrentThread.h>

namespace muduo
{

Exception::Exception(string msg)
  : message_(std::move(msg)),
    stack_(CurrentThread::stackTrace(/*demangle=*/false))
{
}

}  // namespace muduo

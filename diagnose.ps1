# Simple PowerShell Diagnostics for Muduo Project

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Muduo Project Diagnostics (PowerShell)" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Current directory
Write-Host "Current directory: $(Get-Location)" -ForegroundColor Yellow
Write-Host ""

# Check project structure
Write-Host "Checking project files..." -ForegroundColor Yellow

if (Test-Path "muduo") {
    Write-Host "[OK] muduo directory found" -ForegroundColor Green
} else {
    Write-Host "[MISSING] muduo directory not found" -ForegroundColor Red
}

if (Test-Path "examples") {
    Write-Host "[OK] examples directory found" -ForegroundColor Green
} else {
    Write-Host "[MISSING] examples directory not found" -ForegroundColor Red
}

if (Test-Path "CMakeLists.txt") {
    Write-Host "[OK] CMakeLists.txt found" -ForegroundColor Green
} else {
    Write-Host "[MISSING] CMakeLists.txt not found" -ForegroundColor Red
}

Write-Host ""

# Check Git installation
Write-Host "Checking Git installation..." -ForegroundColor Yellow

try {
    $gitVersion = git --version 2>$null
    if ($gitVersion) {
        Write-Host "[OK] Git is installed: $gitVersion" -ForegroundColor Green
    } else {
        Write-Host "[MISSING] Git is not installed" -ForegroundColor Red
    }
}
catch {
    Write-Host "[MISSING] Git is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Git from: https://git-scm.com/download/win" -ForegroundColor Yellow
}

Write-Host ""

# Check Git repository
Write-Host "Checking Git repository..." -ForegroundColor Yellow

if (Test-Path ".git") {
    Write-Host "[OK] Git repository initialized" -ForegroundColor Green
    
    # Check Git config
    try {
        $userName = git config user.name 2>$null
        $userEmail = git config user.email 2>$null
        
        if ($userName) {
            Write-Host "[OK] Git user name configured: $userName" -ForegroundColor Green
        } else {
            Write-Host "[MISSING] Git user name not configured" -ForegroundColor Yellow
        }
        
        if ($userEmail) {
            Write-Host "[OK] Git user email configured: $userEmail" -ForegroundColor Green
        } else {
            Write-Host "[MISSING] Git user email not configured" -ForegroundColor Yellow
        }
    }
    catch {
        Write-Host "[WARNING] Could not check Git configuration" -ForegroundColor Yellow
    }
} else {
    Write-Host "[MISSING] Git repository not initialized" -ForegroundColor Red
}

Write-Host ""

# Check network connectivity
Write-Host "Checking network connectivity..." -ForegroundColor Yellow

try {
    $ping = Test-Connection -ComputerName "github.com" -Count 1 -Quiet
    if ($ping) {
        Write-Host "[OK] Can reach GitHub" -ForegroundColor Green
    } else {
        Write-Host "[WARNING] Cannot reach GitHub" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "[WARNING] Network test failed" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Diagnosis Complete" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Recommendations
Write-Host "What to do next:" -ForegroundColor Yellow
Write-Host ""

$gitInstalled = $false
$repoInitialized = $false

try {
    git --version 2>$null | Out-Null
    $gitInstalled = $true
}
catch {
    $gitInstalled = $false
}

$repoInitialized = Test-Path ".git"

if (-not $gitInstalled) {
    Write-Host "1. Install Git:" -ForegroundColor White
    Write-Host "   - Download from: https://git-scm.com/download/win" -ForegroundColor White
    Write-Host "   - Install with default settings" -ForegroundColor White
    Write-Host "   - Restart PowerShell/Command Prompt" -ForegroundColor White
    Write-Host ""
}

if ($gitInstalled -and -not $repoInitialized) {
    Write-Host "2. Initialize Git repository:" -ForegroundColor White
    Write-Host "   - Run: git init" -ForegroundColor White
    Write-Host "   - Run: git add ." -ForegroundColor White
    Write-Host "   - Run: git commit -m 'Initial commit'" -ForegroundColor White
    Write-Host ""
}

if ($gitInstalled -and $repoInitialized) {
    Write-Host "3. Set up GitHub repository:" -ForegroundColor White
    Write-Host "   - Your system appears ready!" -ForegroundColor Green
    Write-Host "   - Try running the GitHub setup script" -ForegroundColor White
    Write-Host ""
}

Write-Host "Available scripts:" -ForegroundColor Yellow
Write-Host "- test_basic.bat        : Test if batch scripts work" -ForegroundColor White
Write-Host "- simple_diagnose.bat   : Simple batch diagnostics" -ForegroundColor White
Write-Host "- diagnose.ps1          : This PowerShell diagnostic" -ForegroundColor White
Write-Host ""

Read-Host "Press Enter to exit"

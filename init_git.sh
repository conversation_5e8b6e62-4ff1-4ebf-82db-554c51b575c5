#!/bin/bash

echo "Initializing Git repository for Muduo project..."
echo

# Check if Git is installed
if ! command -v git &> /dev/null; then
    echo "Error: Git is not installed."
    echo "Please install Git:"
    echo "  Ubuntu/Debian: sudo apt-get install git"
    echo "  CentOS/RHEL:   sudo yum install git"
    echo "  macOS:         brew install git"
    echo
    exit 1
fi

# Initialize Git repository
echo "[1/6] Initializing Git repository..."
git init
if [ $? -ne 0 ]; then
    echo "Error: Failed to initialize Git repository."
    exit 1
fi

# Set up Git configuration (optional - user can modify)
echo "[2/6] Setting up Git configuration..."
echo "Please enter your Git configuration:"
read -p "Enter your name: " username
read -p "Enter your email: " email

git config user.name "$username"
git config user.email "$email"

# Add all files to staging area
echo "[3/6] Adding files to staging area..."
git add .

# Create initial commit
echo "[4/6] Creating initial commit..."
git commit -m "Initial commit: Add Muduo network library

- Add complete Muduo source code
- Add comprehensive .gitignore for C++ projects
- Add detailed README.md with usage examples
- Set up CMake build system
- Include examples and tests"

# Show repository status
echo "[5/6] Repository status:"
git status

# Show commit log
echo "[6/6] Commit history:"
git log --oneline

echo
echo "========================================"
echo "Git repository initialized successfully!"
echo "========================================"
echo
echo "Next steps:"
echo "1. Create a remote repository on GitHub/GitLab"
echo "2. Add remote origin: git remote add origin [URL]"
echo "3. Push to remote: git push -u origin main"
echo
echo "Repository location: $(pwd)"
echo

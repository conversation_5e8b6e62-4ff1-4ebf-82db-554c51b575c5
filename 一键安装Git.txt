🚀 Git 一键安装指南
====================

我已经为您创建了自动化的Git安装脚本，请按照以下步骤操作：

📋 方法1：使用批处理脚本（推荐）
================================

1. 右键点击 "install_git.bat" 文件
2. 选择 "以管理员身份运行"
3. 按照屏幕提示操作
4. 脚本会自动：
   - 检测并安装Git
   - 配置Git用户信息
   - 初始化Git仓库
   - 创建初始提交

📋 方法2：使用PowerShell脚本
============================

1. 右键点击开始菜单
2. 选择 "Windows PowerShell (管理员)"
3. 导航到项目目录：
   cd "d:\works\muduo\muduo\muduo-master"
4. 运行脚本：
   .\install_git.ps1
5. 如果提示执行策略错误，先运行：
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

📋 方法3：手动安装（备用方案）
==============================

如果自动脚本失败，请手动安装：

1. 访问：https://git-scm.com/download/win
2. 下载 "64-bit Git for Windows Setup"
3. 运行安装程序，使用以下设置：
   ✅ 选择 "Git from the command line and also from 3rd-party software"
   ✅ 选择 "Use the OpenSSL library"
   ✅ 选择 "Checkout Windows-style, commit Unix-style line endings"
4. 安装完成后，重新打开命令提示符
5. 运行：git --version 验证安装
6. 然后运行：init_git.bat 初始化仓库

🔧 安装后验证
==============

安装完成后，请验证以下命令：

git --version
git config --list
git status

如果都能正常运行，说明安装成功！

📞 如果遇到问题
================

常见问题解决：

1. "不是内部或外部命令"
   - 重启命令提示符
   - 检查PATH环境变量是否包含Git路径

2. "拒绝访问"
   - 以管理员身份运行脚本
   - 关闭杀毒软件的实时保护

3. "执行策略"错误（PowerShell）
   - 运行：Set-ExecutionPolicy RemoteSigned

4. 下载失败
   - 检查网络连接
   - 尝试手动下载安装

🎯 安装成功后的下一步
====================

1. Git仓库已初始化 ✅
2. 创建GitHub/GitLab账户
3. 创建远程仓库
4. 连接本地和远程仓库：
   git remote add origin [您的仓库URL]
   git push -u origin main

💡 提示
========

- 脚本会自动处理大部分配置
- 如有疑问，请查看 GIT_SETUP_GUIDE.md
- 所有操作都是安全的，不会损坏现有代码

祝您使用愉快！🎉

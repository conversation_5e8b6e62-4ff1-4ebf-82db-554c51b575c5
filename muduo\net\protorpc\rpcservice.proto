package muduo.net;

option cc_generic_services = true;
option java_generic_services = true;
option py_generic_services = true;

option java_package = "com.chenshuo.muduo.protorpc";
option java_outer_classname = "RpcServiceProto";

//import "google/protobuf/descriptor.proto";
import "rpc.proto";

message ListRpcRequest
{
  optional string service_name = 1;
  optional bool list_method = 2;
}

message ListRpcResponse
{
  required ErrorCode error = 1;
  repeated string service_name = 2;
  repeated string method_name = 3;
}

message GetServiceRequest
{
  required string service_name = 1;
}

message GetServiceResponse
{
  required ErrorCode error = 1;
  repeated string proto_file = 2;
  repeated string proto_file_name = 3;
}

// the meta service
service RpcService
{
  rpc listRpc (ListRpcRequest) returns (ListRpcResponse);
  rpc getService (GetServiceRequest) returns (GetServiceResponse);
}


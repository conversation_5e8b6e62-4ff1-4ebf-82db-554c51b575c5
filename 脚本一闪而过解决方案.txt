🚨 脚本一闪而过问题解决方案
===============================

如果所有批处理脚本都一闪而过，请按以下步骤排查：

📋 第一步：测试基本功能
======================

1. 双击运行：test_basic.bat
   - 如果这个脚本也一闪而过，说明是系统环境问题
   - 如果能正常显示，说明是脚本语法问题

2. 如果test_basic.bat也闪退，请继续下面的步骤

📋 第二步：检查系统环境
======================

可能的原因：
1. 杀毒软件阻止批处理脚本执行
2. Windows安全策略限制
3. 命令提示符环境异常
4. 文件编码问题
5. 文件路径包含特殊字符

解决方案：

方案1：临时关闭杀毒软件
- 暂时关闭实时保护
- 将项目目录添加到白名单
- 重新运行脚本

方案2：使用PowerShell替代
- 右键点击开始菜单
- 选择"Windows PowerShell"
- 运行：.\diagnose.ps1

方案3：手动在命令提示符中运行
- 按Win+R，输入cmd，回车
- 使用cd命令导航到项目目录
- 输入脚本名称运行

📋 第三步：使用PowerShell诊断
============================

如果批处理脚本都有问题，使用PowerShell：

1. 右键点击开始菜单
2. 选择"Windows PowerShell"
3. 导航到项目目录：
   cd "d:\works\muduo\muduo\muduo-master"
4. 运行诊断：
   .\diagnose.ps1

如果提示执行策略错误：
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

📋 第四步：手动检查系统状态
==========================

在命令提示符或PowerShell中手动运行：

检查当前目录：
dir

检查Git安装：
git --version

检查项目文件：
dir muduo
dir examples
dir CMakeLists.txt

检查Git仓库：
dir .git

📋 第五步：手动执行设置步骤
==========================

如果脚本都无法运行，请手动执行：

1. 安装Git（如果未安装）：
   - 访问：https://git-scm.com/download/win
   - 下载并安装

2. 初始化Git仓库（如果未初始化）：
   git init
   git add .
   git commit -m "Initial commit"

3. 配置Git用户信息：
   git config --global user.name "Your Name"
   git config --global user.email "<EMAIL>"

4. 创建GitHub仓库：
   - 访问：https://github.com/new
   - 创建新仓库

5. 连接远程仓库：
   git remote add origin https://github.com/username/repo.git
   git push -u origin main

📋 第六步：替代解决方案
======================

如果批处理脚本完全无法使用：

方案1：使用Git Bash
- 安装Git后会自带Git Bash
- 在项目目录右键选择"Git Bash Here"
- 使用Linux风格的命令

方案2：使用图形界面工具
- GitHub Desktop: https://desktop.github.com/
- SourceTree: https://www.sourcetreeapp.com/
- VS Code的Git扩展

方案3：使用在线工具
- 直接在GitHub网页上创建仓库
- 使用GitHub的文件上传功能

📋 常见问题及解决
================

问题1：脚本闪退但没有错误信息
解决：在命令提示符中手动运行脚本

问题2：提示"不是内部或外部命令"
解决：检查PATH环境变量，重启命令提示符

问题3：权限被拒绝
解决：以管理员身份运行命令提示符

问题4：文件找不到
解决：确认在正确的目录中，检查文件是否存在

问题5：编码问题导致乱码
解决：使用PowerShell或修改命令提示符编码

📋 紧急手动设置流程
==================

如果所有自动化脚本都失败，按此流程手动设置：

1. 确认Git已安装：
   git --version

2. 初始化仓库：
   git init

3. 添加文件：
   git add .

4. 创建提交：
   git commit -m "Initial commit"

5. 在GitHub创建仓库（网页操作）

6. 添加远程仓库：
   git remote add origin [你的仓库URL]

7. 推送代码：
   git push -u origin main

📞 获取帮助
============

如果问题仍然存在：

1. 检查Windows版本和更新
2. 尝试在另一台电脑上运行
3. 使用PowerShell替代批处理
4. 考虑使用图形界面Git工具
5. 查看Windows事件查看器中的错误日志

💡 预防措施
============

为避免类似问题：
- 定期更新Windows系统
- 使用标准的文件路径（避免特殊字符）
- 保持杀毒软件更新但适当配置例外
- 学习基本的命令行操作
- 备份重要的脚本和配置

🎯 成功标志
===========

设置成功的标志：
✅ git --version 显示版本信息
✅ git status 显示仓库状态
✅ 能在GitHub上看到仓库
✅ 能正常推送和拉取代码

如果达到以上标准，说明设置成功！

@echo off
echo ========================================
echo Complete Muduo Project Setup
echo ========================================
echo.
echo This script will guide you through the complete setup process:
echo 1. Check system requirements
echo 2. Install Git (if needed)
echo 3. Initialize Git repository (if needed)
echo 4. Set up GitHub repository
echo.
echo Press any key to start the diagnosis...
pause >nul
echo.

REM Run diagnosis first
echo ========================================
echo Step 1: System Diagnosis
echo ========================================
echo.
call diagnose_setup.bat
echo.

REM Check if Git is installed
echo ========================================
echo Step 2: Git Installation Check
echo ========================================
echo.
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Git is not installed. Starting Git installation...
    echo.
    if exist "install_git.bat" (
        echo Running Git installer...
        call install_git.bat
        echo.
        echo Checking Git installation again...
        git --version >nul 2>&1
        if %errorlevel% neq 0 (
            echo ❌ Git installation failed or incomplete.
            echo Please manually install Git and restart this script.
            echo.
            pause
            exit /b 1
        )
    ) else (
        echo ❌ install_git.bat not found.
        echo Please manually install Git from: https://git-scm.com/download/win
        echo Then restart this script.
        echo.
        pause
        exit /b 1
    )
) else (
    echo ✅ Git is already installed.
    git --version
)
echo.

REM Check if repository is initialized
echo ========================================
echo Step 3: Git Repository Initialization
echo ========================================
echo.
if not exist ".git" (
    echo Git repository is not initialized. Starting initialization...
    echo.
    if exist "init_git.bat" (
        echo Running Git repository initializer...
        call init_git.bat
        echo.
        if not exist ".git" (
            echo ❌ Git repository initialization failed.
            echo Please check the error messages above.
            echo.
            pause
            exit /b 1
        )
    ) else (
        echo ❌ init_git.bat not found.
        echo Initializing repository manually...
        git init
        git add .
        git commit -m "Initial commit: Add Muduo network library"
        if %errorlevel% neq 0 (
            echo ❌ Manual repository initialization failed.
            echo.
            pause
            exit /b 1
        )
    )
) else (
    echo ✅ Git repository is already initialized.
)
echo.

REM Check Git configuration
echo ========================================
echo Step 4: Git Configuration Check
echo ========================================
echo.
git config user.name >nul 2>&1
if %errorlevel% neq 0 (
    echo Git user name is not configured.
    set /p git_name="Enter your name for Git commits: "
    git config --global user.name "%git_name%"
)

git config user.email >nul 2>&1
if %errorlevel% neq 0 (
    echo Git user email is not configured.
    set /p git_email="Enter your email for Git commits: "
    git config --global user.email "%git_email%"
)

echo ✅ Git configuration:
echo Name: 
git config user.name
echo Email: 
git config user.email
echo.

REM Set up GitHub repository
echo ========================================
echo Step 5: GitHub Repository Setup
echo ========================================
echo.
echo All prerequisites are met. Starting GitHub setup...
echo.
if exist "setup_github_repo.bat" (
    call setup_github_repo.bat
) else (
    echo ❌ setup_github_repo.bat not found.
    echo Please run the GitHub setup manually:
    echo 1. Create repository on GitHub
    echo 2. Run: git remote add origin [URL]
    echo 3. Run: git push -u origin main
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo Your Muduo project should now be:
echo ✅ Git repository initialized
echo ✅ Connected to GitHub
echo ✅ Ready for development
echo.
echo Next steps:
echo - Start coding and making commits
echo - Invite collaborators to your repository
echo - Set up continuous integration
echo - Create issues and project boards
echo.
pause

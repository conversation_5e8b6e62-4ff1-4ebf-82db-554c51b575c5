Muduo is a multithreaded C++ network library based on
the reactor pattern.

http://github.com/chenshuo/muduo

Copyright (c) 2010, <PERSON><PERSON>.  All rights reserved.

Use of this source code is governed by a BSD-style
license that can be found in the License file.

Requires:
  Linux kernel version >= 2.6.28.
  GCC >= 4.7 or Clang >= 3.5
  Boost (for boost::any only.)

Tested on:
  Debian 7 and above
  Unbuntu 14.04 and above
  CentOS 7 and above

To build, run:
  ./build.sh

  __  __           _
 |  \/  |         | |
 | \  / |_   _  __| |_   _  ___
 | |\/| | | | |/ _` | | | |/ _ \
 | |  | | |_| | (_| | |_| | (_) |
 |_|  |_|\__,_|\__,_|\__,_|\___/


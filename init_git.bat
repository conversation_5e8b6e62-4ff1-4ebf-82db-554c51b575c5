@echo off
echo Initializing Git repository for Muduo project...
echo.

REM Check if Git is installed
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Git is not installed or not in PATH.
    echo Please install Git from: https://git-scm.com/download/win
    echo.
    pause
    exit /b 1
)

REM Initialize Git repository
echo [1/6] Initializing Git repository...
git init
if %errorlevel% neq 0 (
    echo Error: Failed to initialize Git repository.
    pause
    exit /b 1
)

REM Set up Git configuration (optional - user can modify)
echo [2/6] Setting up Git configuration...
echo Please enter your Git configuration:
set /p username="Enter your name: "
set /p email="Enter your email: "

git config user.name "%username%"
git config user.email "%email%"

REM Add all files to staging area
echo [3/6] Adding files to staging area...
git add .

REM Create initial commit
echo [4/6] Creating initial commit...
git commit -m "Initial commit: Add Muduo network library

- Add complete Muduo source code
- Add comprehensive .gitignore for C++ projects
- Add detailed README.md with usage examples
- Set up CMake build system
- Include examples and tests"

REM Show repository status
echo [5/6] Repository status:
git status

REM Show commit log
echo [6/6] Commit history:
git log --oneline

echo.
echo ========================================
echo Git repository initialized successfully!
echo ========================================
echo.
echo Next steps:
echo 1. Create a remote repository on GitHub/GitLab
echo 2. Add remote origin: git remote add origin [URL]
echo 3. Push to remote: git push -u origin main
echo.
echo Repository location: %CD%
echo.
pause

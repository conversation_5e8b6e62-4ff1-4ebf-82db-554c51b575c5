# Muduo Network Library

Muduo is a multithreaded C++ network library based on the reactor pattern.

## Overview

Muduo is a high-quality network programming library designed for Linux platforms. It provides:

- **Reactor Pattern**: Event-driven non-blocking I/O model
- **One Loop Per Thread**: Each thread runs one event loop
- **High Performance**: Optimized for Linux using epoll
- **Thread Safety**: Carefully designed thread-safe mechanisms
- **Easy to Use**: Clean and intuitive API

## Features

- ✅ Modern C++11 design
- ✅ High-performance network I/O
- ✅ Thread-safe logging system
- ✅ Flexible timer management
- ✅ HTTP server support
- ✅ Protocol buffer integration
- ✅ Comprehensive examples

## Requirements

- Linux kernel version >= 2.6.28
- GCC >= 4.7 or Clang >= 3.5
- CMake >= 2.6
- Boost (for boost::any only)

## Tested Platforms

- Debian 7 and above
- Ubuntu 14.04 and above
- CentOS 7 and above

## Building

```bash
./build.sh
```

Or using CMake directly:

```bash
mkdir build && cd build
cmake ..
make
```

## Quick Start

### Simple Echo Server

```cpp
#include <muduo/net/TcpServer.h>
#include <muduo/net/EventLoop.h>

class EchoServer {
public:
    EchoServer(muduo::net::EventLoop* loop,
               const muduo::net::InetAddress& listenAddr)
        : server_(loop, listenAddr, "EchoServer") {
        server_.setConnectionCallback(
            std::bind(&EchoServer::onConnection, this, _1));
        server_.setMessageCallback(
            std::bind(&EchoServer::onMessage, this, _1, _2, _3));
    }

    void start() { server_.start(); }

private:
    void onConnection(const muduo::net::TcpConnectionPtr& conn) {
        // Handle new connections
    }

    void onMessage(const muduo::net::TcpConnectionPtr& conn,
                   muduo::net::Buffer* buf,
                   muduo::Timestamp time) {
        // Echo back the received message
        conn->send(buf->retrieveAllAsString());
    }

    muduo::net::TcpServer server_;
};

int main() {
    muduo::net::EventLoop loop;
    muduo::net::InetAddress listenAddr(2007);
    EchoServer server(&loop, listenAddr);
    server.start();
    loop.loop();
}
```

## Architecture

### Core Modules

- **muduo/base**: Basic utilities (threads, logging, time, etc.)
- **muduo/net**: Network components (EventLoop, TcpServer, Buffer, etc.)

### Key Components

- **EventLoop**: The heart of the Reactor pattern
- **Channel**: Abstraction for I/O events
- **Buffer**: Efficient buffer management
- **TcpServer**: High-level TCP server
- **TcpConnection**: TCP connection management

## Examples

The `examples/` directory contains various usage examples:

- **simple/echo**: Basic echo server
- **simple/daytime**: Daytime server
- **simple/discard**: Discard server
- **http**: HTTP server implementation
- **pingpong**: Performance testing
- **sudoku**: Sudoku solver server

## Documentation

- Original project: http://github.com/chenshuo/muduo
- API documentation: See header files for detailed comments

## License

Use of this source code is governed by a BSD-style license that can be found in the License file.

## Author

Copyright (c) 2010, Shuo Chen. All rights reserved.

---

```
  __  __           _
 |  \/  |         | |
 | \  / |_   _  __| |_   _  ___
 | |\/| | | | |/ _` | | | |/ _ \
 | |  | | |_| | (_| | |_| | (_) |
 |_|  |_|\__,_|\__,_|\__,_|\___/
```
